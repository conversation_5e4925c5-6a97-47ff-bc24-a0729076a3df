package com.thedasagroup.suminative.ui.common

import android.security.keystore.KeyProperties
import android.util.Base64
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.ui.login.LoginUseCase
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.security.KeyFactory
import java.security.PrivateKey
import java.security.PublicKey
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import javax.crypto.Cipher

class CommonViewModel @AssistedInject constructor(
    @Assisted state: CommonState, val loginUseCase: LoginUseCase, val prefs: Prefs
) : MavericksViewModel<CommonState>(state) {

    fun updateShowSuccessDialog(show: Boolean) {
        setState {
            copy(
                showSuccessDialog = show,
            )
        }
    }

    fun updateSuccessDialogMessage(message: String) {
        setState {
            copy(
                successDialogMessage = message,
            )
        }
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<CommonViewModel, CommonState> {
        override fun create(state: CommonState): CommonViewModel
    }

    companion object :
        MavericksViewModelFactory<CommonViewModel, CommonState> by hiltMavericksViewModelFactory()
}

data class CommonState(
    val showSuccessDialog : Boolean = false,
    val successDialogMessage : String = "",
) : MavericksState