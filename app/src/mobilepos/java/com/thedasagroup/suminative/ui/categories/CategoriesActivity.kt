package com.thedasagroup.suminative.ui.categories

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Surface
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.viewModel
import com.google.android.material.snackbar.Snackbar
import com.thedasagroup.suminative.BuildConfig
import com.thedasagroup.suminative.data.model.request.cloud_print.CloudPrintRequest
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.pagination.Customer
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem2
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.data.model.response.store_orders.Order2
import com.thedasagroup.suminative.ui.common.SuccessAvailDialog
import com.thedasagroup.suminative.ui.common.SuccessDialog
import com.thedasagroup.suminative.ui.lcd.LcdViewModel
import com.thedasagroup.suminative.ui.payment.PaymentCompose
import com.thedasagroup.suminative.ui.payment.PaymentViewModel
import com.thedasagroup.suminative.ui.payment.SumUpPaymentHelper
import com.thedasagroup.suminative.ui.printer.selectPrinter
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.products.cart.CartScreenFigma
import com.thedasagroup.suminative.ui.rewards.RewardsState
import com.thedasagroup.suminative.ui.rewards.RewardsViewModel
import com.thedasagroup.suminative.ui.splitbill.SplitBillActivity
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import com.thedasagroup.suminative.ui.utils.transformDecimal
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@OptIn(ExperimentalMaterial3Api::class)
class CategoriesActivity : AppCompatActivity(), MavericksView {

    private val productsScreenViewModel: ProductsScreenViewModel by viewModel()
    val paymentViewModel: PaymentViewModel by viewModel()
    val rewardsViewModel: RewardsViewModel by viewModel()
    private val lcdViewModel: LcdViewModel by viewModels()

    // Callback to close cart from activity level
    private var closeCartCallback: (() -> Unit)? = null

    // Success dialog callback
    private var showSuccessDialogCallback: ((String) -> Unit)? = null
    private var showSuccessAvailDialogCallback: ((String) -> Unit)? = null

    // Store current order for SumUp payment flow
    private var currentSumUpOrder: Order? = null

    // Processing dialog for order saving
    private var processingDialog: com.afollestad.materialdialogs.MaterialDialog? = null

    // Store QR code scan result for rewards
    private var qrCodeScanResult: String? = null

    // Activity result launcher for cash payment
    private val cashPaymentLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        paymentViewModel.showChangeDialog(showChangeDialog = false)

        if (result.resultCode == RESULT_OK) {
            val completedOrder =
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                    result.data?.getParcelableExtra(
                        com.thedasagroup.suminative.ui.payment.CashPaymentActivity.RESULT_ORDER,
                        Order::class.java
                    )
                } else {
                    @Suppress("DEPRECATION")
                    result.data?.getParcelableExtra(com.thedasagroup.suminative.ui.payment.CashPaymentActivity.RESULT_ORDER)
                }

            completedOrder?.let { finalOrder ->
                handleCashPaymentComplete(finalOrder)
            }
        }
    }

    // Activity result launcher for card payment
    private val cardPaymentLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val completedOrder =
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                    result.data?.getParcelableExtra(
                        com.thedasagroup.suminative.ui.payment.PaymentActivity.RESULT_ORDER,
                        Order2::class.java
                    )
                } else {
                    @Suppress("DEPRECATION")
                    result.data?.getParcelableExtra(com.thedasagroup.suminative.ui.payment.PaymentActivity.RESULT_ORDER)
                }

            completedOrder?.let { finalOrder ->
                handleCardPaymentComplete(finalOrder)
            }
        }
    }


    val sumniQRCodeScanLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val data = result.data
            val bundle: Bundle = data!!.getExtras()!!
            val result = bundle.getSerializable("data") as ArrayList<*>?
            val it: MutableIterator<*> = result!!.iterator()
            var scannedData : String = ""
            while (it.hasNext()) {
                val hashMap = it.next() as HashMap<*, *>
                Log.i("sunmi", (hashMap.get("TYPE") as String?) ?: "") //Scan type
                Log.i("sunmi", (hashMap.get("VALUE") as String?) ?: "") //Scan result
                scannedData = (hashMap.get("VALUE") as String?) ?: ""
            }
            scannedData?.let { scannedData ->
                // Pass the QR code result to rewards screen
                handleQRCodeScanResult(scannedData)
            }
        }
    }


    // Removed sumUpPaymentLauncher - now using direct SumUp SDK integration

    private fun handleCashPaymentComplete(finalOrder: Order) {
        lifecycleScope.launch {
            productsScreenViewModel.placeOrderOffline(
                order = finalOrder
            ).collectLatest {
                if (it is Success) {
                    withContext(Dispatchers.Main) {
                        if (BuildConfig.SHOW_PRINTBILL.isNotEmpty()) {
                            selectPrinter?.cashDrawerApi()?.open(null)
                        }
                    }
                    launch {
                        productsScreenViewModel.updateShowPrintingPreview(
                            order = OrderItem2(
                                customer = Customer(
                                    name = "Walk-in Customer (In Store)",
                                    phone = ""
                                ), order = it().order!!
                            )
                        )
                        // Clear cart and close modal
                        productsScreenViewModel.clearCart()
                        productsScreenViewModel.updateCartVisibility(false)

                        withContext(Dispatchers.Main) {
                            // Close the cart modal if it's open
                            closeCartModal()
                            // Show success dialog
                            showSuccessDialogCallback?.invoke("Order Placed Successfully")
                        }
                    }
                }
            }
        }
    }

    private fun handleCardPaymentComplete(finalOrder: Order2) {
        lifecycleScope.launch {
            withContext(Dispatchers.IO) {
                productsScreenViewModel.updateShowPrintingPreview(
                    order = OrderItem2(
                        customer = Customer(
                            name = "Walk-in Customer (In Store)",
                            phone = ""
                        ), order = finalOrder
                    )
                )
            }
            // Clear cart and close modal
            productsScreenViewModel.clearCart()
            productsScreenViewModel.updateCartVisibility(false)

            withContext(Dispatchers.Main) {
                // Close the cart modal if it's open
                closeCartModal()
                // Show success dialog
                showSuccessDialogCallback?.invoke("Order Placed Successfully")
            }
        }
    }

    private fun closeCartModal() {
        closeCartCallback?.invoke()
    }

    private fun handleQRCodeScanResult(scannedData: String) {
        // Store the QR code result
        qrCodeScanResult = scannedData

        // Extract customer ID from QR code (assuming format like "dasa-businessId-customerId")
        val customerId = extractCustomerId(scannedData)
        val businessId = extractBusinessId(scannedData)

        if (customerId != null) {
            // Set the customer ID input in rewards view model
            rewardsViewModel.updateCustomerIdInput(scannedData)

            // Automatically search for the customer
            lifecycleScope.launch {
                rewardsViewModel.getAllCustomers(customerId = customerId, businessId = businessId ?: productsScreenViewModel.prefs?.store?.businessId ?: 0 )
            }
        }

        // Show rewards dialog with the scanned data
        showRewardsDialog()
    }

    private fun showRewardsDialog() {
        rewardsViewModel.showRewardsDialog(show = true)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        setContent {
            val showCategoryProducts = remember { mutableStateOf(false) }
            val selectedCategory = remember { mutableStateOf("") }
            val selectedProducts = remember { mutableStateOf<List<StockItem>>(emptyList()) }
            val showCart = remember { mutableStateOf(false) }
            val showSuccessDialog = remember { mutableStateOf(false) }
            val successDialogMessage = remember { mutableStateOf("Order Placed Successfully") }

            val showSuccessAvailDialog = remember { mutableStateOf(false) }
            val successDialogAvailMessage = remember { mutableStateOf("Order Placed Successfully") }

            val cartSheetState = rememberModalBottomSheetState(
                skipPartiallyExpanded = true
            )
            val coroutineScope = rememberCoroutineScope()

            // Set up the close cart callback
            closeCartCallback = {
                coroutineScope.launch {
                    showCart.value = false
                    cartSheetState.hide()
                    productsScreenViewModel.updateCartVisibility(false)
                }
            }

            // Set up the success dialog callback
            showSuccessDialogCallback = { message ->
                successDialogMessage.value = message
                showSuccessDialog.value = true
            }

            showSuccessAvailDialogCallback = { message ->
                successDialogAvailMessage.value = message
                showSuccessAvailDialog.value = true
            }

            SumiNativeTheme {

                val snackbarHostState = remember { SnackbarHostState() }

                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val order by productsScreenViewModel.collectAsState { it.getCurrentTableOrder() }
                    val selectedTables by productsScreenViewModel.collectAsState(ProductsScreenState::selectedTables)
                    val selectedTableIndex by productsScreenViewModel.collectAsState(
                        ProductsScreenState::selectedTableIndex
                    )
                    val tableOrders by productsScreenViewModel.collectAsState(ProductsScreenState::tableOrders)
                    val availableCourses by productsScreenViewModel.collectAsState { it.getCurrentTableAvailableCourses() }
                    val cartItemsWithCourses by productsScreenViewModel.collectAsState(
                        ProductsScreenState::cartItemsWithCourses
                    )
                    val globalCartItemsWithCourses by productsScreenViewModel.collectAsState(
                        ProductsScreenState::globalCartItemsWithCourses
                    )
                    val state by productsScreenViewModel.collectAsState()
                    val showRewardsDialog by rewardsViewModel.collectAsState(RewardsState::showRewardsDialog)
                    val rewardsState by rewardsViewModel.collectAsState()

                    if (showCategoryProducts.value) {
                        CategoryProductsScreen(
                            categoryName = selectedCategory.value,
                            products = selectedProducts.value,
                            onBackClick = {
                                showCategoryProducts.value = false
                            },
                            onProductClick = { product ->
                                // Handle product click - open product details
                                productsScreenViewModel.updateProductDetailsBottomSheetVisibility(
                                    stockItem = product
                                )
                            },
                            rewardsViewModel = rewardsViewModel,
                            viewModel = productsScreenViewModel,
                            onAddToCart = { stockItem, optionDetails ->
                                // Add item to cart with selected options
                                productsScreenViewModel.addItemToCart(
                                    order = order,
                                    stockItem = stockItem,
                                    quantity = 1,
                                    optionDetails = optionDetails,
                                    selectedTables = selectedTables,
                                    selectedTableIndex = selectedTableIndex,
                                    tableOrders = tableOrders,
                                    state = state
                                )
                                // Show success dialog
                                showSuccessDialogCallback?.invoke("${stockItem.name} - Added To Cart")
                            },
                            onOpenCart = {
                                coroutineScope.launch {
                                    showCart.value = true
                                    cartSheetState.show()
                                }
                            },
                            onRewardsClick = {
                                if (rewardsState.selectedCustomer == null &&
                                    rewardsState.customerIdInput.isEmpty() &&
                                    !rewardsState.isLoading) {
                                    startQRCodeScan()
                                }
                                else {
                                    rewardsViewModel.showRewardsDialog(show = true)
                                }
                            },
                            onRemoveCustomer = {
                                rewardsViewModel.resetState()
                            },
                            onAvailToCartSuccess = {
                                showSuccessAvailDialogCallback?.invoke("${it?.name} - Added To Cart")
                            }
                        )
                    } else {
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(Color(0xFFF5F5F5))
                                .statusBarsPadding()
                        ) {
                            CategoriesScreen(
                                viewModel = productsScreenViewModel,
                                rewardsViewModel = rewardsViewModel,
                                onBackClick = {
                                    finish()
                                },
                                onCategoryClick = { categoryName, items ->
                                    selectedCategory.value = categoryName
                                    selectedProducts.value = items
                                    showCategoryProducts.value = true
                                },
                                onOpenCart = {
                                    coroutineScope.launch {
                                        showCart.value = true
                                        cartSheetState.show()
                                    }
                                },
                                onAvailToCartSuccess = {
                                    showSuccessAvailDialogCallback?.invoke("${it?.name} - Added To Cart")
                                },
                                onRemoveCustomer = {
                                    rewardsViewModel.resetState()
                                },
                                onRewardsClick = {
                                    if (rewardsState.selectedCustomer == null &&
                                        rewardsState.customerIdInput.isEmpty() &&
                                        !rewardsState.isLoading) {
                                        startQRCodeScan()
                                    }
                                    else {
                                        rewardsViewModel.showRewardsDialog(show = true)
                                    }
                                }
                            )
                        }

                    }

                    // Cart Modal Bottom Sheet
                    if (showCart.value) {
                        ModalBottomSheet(
                            onDismissRequest = {
                                showCart.value = false
                            },
                            sheetState = cartSheetState,
                            modifier = Modifier.fillMaxHeight(0.95f),
                            containerColor = MaterialTheme.colorScheme.background,
                            shape = RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp),
                            dragHandle = {
                                Box(
                                    modifier = Modifier
                                        .padding(vertical = 8.dp)
                                        .width(40.dp)
                                        .height(4.dp)
                                        .background(
                                            MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.4f),
                                            RoundedCornerShape(2.dp)
                                        )
                                )
                            },
                            windowInsets = WindowInsets(0, 0, 0, 0)
                        ) {
                            CartScreenFigma(
                                order = order,
                                onRemoveItem = { cartItem ->
                                    coroutineScope.launch {
                                        productsScreenViewModel.removeItemFromCart(
                                            order = order,
                                            cartItem = cartItem,
                                            selectedTables = selectedTables,
                                            selectedTableIndex = selectedTableIndex,
                                            tableOrders = tableOrders,
                                            currentState = state
                                        ).collectLatest { updatedOrder ->
                                            if (updatedOrder is Success) {
                                                // Show success dialog for item removed
                                                showSuccessDialog.value = true
                                                successDialogMessage.value =
                                                    "Item removed from cart"

                                                withContext(Dispatchers.Main) {
                                                    if (BuildConfig.SHOW_PRINTBILL.isNotEmpty()) {
                                                        lcdViewModel.lcdDigital(
                                                            updatedOrder().totalPrice?.transformDecimal()
                                                                ?: ""
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    }
                                },
                                closeCart = {
                                    coroutineScope.launch {
                                        showCart.value = false
                                        cartSheetState.hide()
                                        productsScreenViewModel.updateCartVisibility(false)
                                        cartSheetState.hide()
                                    }
                                },
                                onUpdateStock = { stock, storeItem, cart, optionDetails ->
                                    productsScreenViewModel.updateCartStock(
                                        order = order,
                                        stock = stock,
                                        stockItem = storeItem,
                                        optionDetails = optionDetails ?: OptionDetails(),
                                        selectedTables = selectedTables,
                                        selectedTableIndex = selectedTableIndex,
                                        tableOrders = tableOrders,
                                        state = state,
                                        cart = cart
                                    )
                                },
                                onUpdateNotes = { cart, notes ->
                                    productsScreenViewModel.updateCartItemNotes(
                                        order = order,
                                        cart = cart,
                                        notes = notes,
                                        selectedTableIndex = selectedTableIndex,
                                        selectedTables = selectedTables,
                                        tableOrders = tableOrders
                                    )
                                },
                                onVoidItem = { cart ->
                                    productsScreenViewModel.voidCartItem(
                                        order = order,
                                        cart = cart,
                                        selectedTables = selectedTables,
                                        selectedTableIndex = selectedTableIndex,
                                        tableOrders = tableOrders
                                    )
                                },
                                placeOrderCash = {
                                    val updatedOrder = order.copy(paymentType = 5)
                                    paymentViewModel.showChangeDialog(showChangeDialog = true)
                                    CashPaymentCompose.showCashPaymentActivity(
                                        activity = this@CategoriesActivity,
                                        order = updatedOrder,
                                        launcher = cashPaymentLauncher
                                    )
                                },
                                placeOrderCard = {
                                    val updatedOrder = order.copy(paymentType = 6)
                                    if (paymentViewModel.isMyGuava()) {
                                        PaymentCompose.showPaymentActivity(
                                            activity = this@CategoriesActivity,
                                            order = updatedOrder,
                                            launcher = cardPaymentLauncher
                                        )
                                    } else if (paymentViewModel.isSumUp()) {
                                        // Store order for auto-payment after login
                                        currentSumUpOrder = updatedOrder

                                        // Direct SumUp login/payment flow - no intermediate activity
                                        if (SumUpPaymentHelper.isLoggedIn()) {
                                            // Already logged in, start payment directly
                                            SumUpPaymentHelper.startPayment(
                                                this@CategoriesActivity,
                                                updatedOrder
                                            )
                                        } else {
                                            // Not logged in, start login first
                                            SumUpPaymentHelper.startLogin(this@CategoriesActivity)
                                        }
                                    } else { // Place order directly
                                        coroutineScope.launch {
                                            productsScreenViewModel.placeOrderOffline(
                                                order = updatedOrder
                                            ).collectLatest {
                                                if (it is Success) {
                                                    coroutineScope.launch(Dispatchers.Main) {
                                                        withContext(Dispatchers.IO) {
                                                            productsScreenViewModel.updateShowPrintingPreview(
                                                                order = OrderItem2(
                                                                    customer = Customer(
                                                                        name = "Walk-in Customer (In Store)",
                                                                        phone = ""
                                                                    ), order = it().order!!
                                                                )
                                                            )
                                                        }
                                                        // Clear cart and close modal
                                                        productsScreenViewModel.clearCart()
                                                        productsScreenViewModel.updateCartVisibility(
                                                            false
                                                        )
                                                        showCart.value = false
                                                        cartSheetState.hide()

                                                        showSuccessDialogCallback?.invoke("Order Placed Successfully")
                                                        // showOrderSucessSnackBar()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                },
                                onSplitBillClick = { numberOfPersons ->
                                    val intent = SplitBillActivity.createIntent(
                                        context = this@CategoriesActivity,
                                        order = order,
                                        numberOfPersons = numberOfPersons
                                    )
                                    startActivity(intent)
                                },
                                onAddNewCourse = { newCourse ->
                                    coroutineScope.launch {
                                        productsScreenViewModel.addNewCourse(
                                            courseName = newCourse,
                                            availableCourses = availableCourses
                                        )
                                    }
                                },
                                onCloudPrintClick = {
                                    coroutineScope.launch {
                                        productsScreenViewModel.cloudPrint(
                                            request = CloudPrintRequest(
                                                storeId = (productsScreenViewModel.prefs?.store?.id
                                                    ?: 0).toString(),
                                                carts = order.carts ?: mutableListOf()
                                            )
                                        ).collectLatest { response ->
                                            if (response is Success) {
                                                showSuccessDialog.value = true
                                                successDialogMessage.value =
                                                    "Order printed successfully"
//                                                commonViewModel.updateShowSuccessDialog(true)
//                                                commonViewModel.updateSuccessDialogMessage("Order printed successfully")
////                                                withContext(Dispatchers.Main) {
//                                                    if (BuildConfig.SHOW_PRINTBILL.isNotEmpty()) {
//                                                        lcdViewModel.lcdDigital(order.totalPrice?.transformDecimal() ?: "")
//                                                    }
//                                                }
                                            } else if (response is Fail) {
                                                showSuccessDialog.value = true
                                                successDialogMessage.value =
                                                    "Failed to print order: ${response.error.message}"
//                                                commonViewModel.updateShowSuccessDialog(true)
//                                                commonViewModel.updateSuccessDialogMessage("Failed to print order: ${response.error.message}")
                                            }
                                        }
                                    }
                                },
                                onCourseBillClick = {
                                    coroutineScope.launch {
                                        productsScreenViewModel.printBill(
                                            selectedTableIndex = selectedTableIndex,
                                            selectedTables = selectedTables,
                                            cartItemsWithCourses = cartItemsWithCourses,
                                            availableCourses = availableCourses,
                                            globalCartItemsWithCourses = globalCartItemsWithCourses
                                        ).collectLatest { result ->
                                            when (result) {
                                                is Success -> {
                                                    withContext(Dispatchers.Main) {
                                                        showSuccessDialog.value = true
                                                        successDialogMessage.value =
                                                            "Print Bill Successfully"
                                                    }
                                                }

                                                is Fail -> {
                                                    withContext(Dispatchers.Main) {
                                                        showSuccessDialog.value = true
                                                        successDialogMessage.value =
                                                            result.error?.message
                                                                ?: "Could not print"
                                                    }
                                                }

                                                else -> {

                                                }
                                            }
                                        }
                                    }
                                },
                                onGoButtonClick = { courseId ->
                                    coroutineScope.launch(Dispatchers.IO) {
                                        productsScreenViewModel.sendCoursesNotificationForCourse(
                                            courseId = courseId,
                                            selectedTables = selectedTables,
                                            selectedTableIndex = selectedTableIndex,
                                            cartItemsWithCourses = cartItemsWithCourses,
                                            globalCartItemsWithCourses = globalCartItemsWithCourses,
                                            availableCourses = availableCourses
                                        ).collectLatest { result ->
                                            when (result) {
                                                is Success -> {
                                                    withContext(Dispatchers.Main) {
                                                        showSuccessDialog.value = true
                                                        successDialogMessage.value =
                                                            "Course Print Sent Successfully"
//                                                        commonViewModel.updateShowSuccessDialog(show = true)
//                                                        commonViewModel.updateSuccessDialogMessage("Course Print Sent Successfully")
                                                    }
                                                }

                                                is Fail -> {
                                                    withContext(Dispatchers.Main) {
                                                        showSuccessDialog.value = true
                                                        successDialogMessage.value =
                                                            result.error?.message
                                                                ?: "Could not print"
//                                                        commonViewModel.updateShowSuccessDialog(show = true)
//                                                        commonViewModel.updateSuccessDialogMessage(result.error?.message ?: "Could not print")
                                                    }
                                                }

                                                else -> {

                                                }
                                            }
                                        }
                                    }
                                },
                                onSendToKitchen = {
                                    coroutineScope.launch {
                                        productsScreenViewModel.sendToKitchen(
                                            selectedTableIndex = selectedTableIndex,
                                            selectedTables = selectedTables,
                                            cartItemsWithCourses = cartItemsWithCourses,
                                            globalCartItemsWithCourses = globalCartItemsWithCourses,
                                            state = state
                                        ).collectLatest { result ->
                                            when (result) {
                                                is Success -> {
                                                    withContext(Dispatchers.Main) {
                                                        showSuccessDialog.value = true
                                                        successDialogMessage.value =
                                                            "Sent to Kitchen Successfully"
//                                                        commonViewModel.updateShowSuccessDialog(show = true)
//                                                        commonViewModel.updateSuccessDialogMessage("Sent to Kitchen Successfully")
                                                    }
                                                }

                                                is Fail -> {
                                                    withContext(Dispatchers.Main) {
                                                        showSuccessDialog.value = true
                                                        successDialogMessage.value =
                                                            result.error?.message
                                                                ?: "Could not send to kitchen"
//                                                        commonViewModel.updateShowSuccessDialog(show = true)
//                                                        commonViewModel.updateSuccessDialogMessage(result.error?.message ?: "Could not sent to kitchen")
                                                    }
                                                }

                                                else -> {

                                                }
                                            }
                                        }
                                    }
                                },
                                productsScreenViewModel = productsScreenViewModel,
                                onApplyServiceChargeClick = { order ->
                                    productsScreenViewModel.applyServiceCharge(state = state)
                                },
                                onRemoveServiceChargeClick = {
                                    productsScreenViewModel.removeServiceCharge(state = state)
                                },
                                state = state
                            )
                        }
                    }

                    // Success Dialog
                    SuccessDialog(
                        message = successDialogMessage.value,
                        isVisible = showSuccessDialog.value,
                        onDismiss = { showSuccessDialog.value = false }
                    )

                    SuccessAvailDialog(
                        message = successDialogAvailMessage.value,
                        isVisible = showSuccessAvailDialog.value,
                        onDismiss = { showSuccessAvailDialog.value = false }
                    )

                    // Rewards Dialog
                    if (showRewardsDialog) {
                        com.thedasagroup.suminative.ui.rewards.RewardsDialog(
                            viewModel = rewardsViewModel,
                            onDismiss = {
                                rewardsViewModel.showRewardsDialog(show = false)
//                                rewardsViewModel.setSelectedCustomer(customer = null)
                                // Clear QR code result when dialog is dismissed
//                                qrCodeScanResult = null
                            },
                            onAddToCart = { rewardItem ->
                                // Add reward item to cart with zero price
                                com.thedasagroup.suminative.ui.rewards.RewardsCartHelper.addRewardItemToCart(
                                    rewardItem = rewardItem,
                                    productsViewModel = productsScreenViewModel,
                                    state = state
                                )
                                // Show success message
                                showSuccessAvailDialogCallback?.invoke("${rewardItem.storeItem?.name} - Added To Cart")
                                // Close rewards dialog
                                rewardsViewModel.showRewardsDialog(show = false)
                            },
                            onStartQRScan = {
                                startQRCodeScan()
                            },
                            onAddPointsClick = {
                                rewardsViewModel.showRewardsDialog(show = false)
                            },
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                }
            }
        }
    }

    override fun invalidate() {

    }

    // Show processing dialog
    private fun showProcessingDialog() {
        processingDialog?.dismiss()
        processingDialog = com.afollestad.materialdialogs.MaterialDialog(this).show {
            title(text = "Processing Order")
            message(text = "Please wait while we save your order...")
            cancelable(false)
            cancelOnTouchOutside(false)
        }
    }

    // Hide processing dialog
    private fun hideProcessingDialog() {
        processingDialog?.dismiss()
        processingDialog = null
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            SumUpPaymentHelper.REQUEST_CODE_SUMUP_LOGIN -> {
                handleSumUpLoginResult(data)
            }

            SumUpPaymentHelper.REQUEST_CODE_SUMUP_PAYMENT -> {
                handleSumUpPaymentResult(data)
            }
        }
    }

    private fun handleSumUpLoginResult(data: Intent?) {
        if (data != null) {
            val extra = data.extras
            val resultCode =
                extra?.getInt(com.sumup.merchant.reader.api.SumUpAPI.Response.RESULT_CODE)
            val message = extra?.getString(com.sumup.merchant.reader.api.SumUpAPI.Response.MESSAGE)
                ?: "Unknown error"

            val success =
                resultCode == com.sumup.merchant.reader.api.SumUpAPI.Response.ResultCode.SUCCESSFUL
            if (success) {
                Toast.makeText(this, "SumUp login successful", Toast.LENGTH_SHORT).show()
                // Auto-start payment if there's an active order
                currentSumUpOrder?.let { order ->
                    SumUpPaymentHelper.startPayment(this, order)
                }
            } else {
                Toast.makeText(this, "SumUp login failed: $message", Toast.LENGTH_LONG).show()
            }
        } else {
            Toast.makeText(this, "SumUp login cancelled", Toast.LENGTH_SHORT).show()
        }
    }

    private fun handleSumUpPaymentResult(data: Intent?) {
        if (data != null) {
            val extra = data.extras
            val resultCode =
                extra?.getInt(com.sumup.merchant.reader.api.SumUpAPI.Response.RESULT_CODE)
            val message = extra?.getString(com.sumup.merchant.reader.api.SumUpAPI.Response.MESSAGE)
                ?: "Unknown error"
            val transactionInfo =
                extra?.getParcelable<com.sumup.merchant.reader.models.TransactionInfo>(com.sumup.merchant.reader.api.SumUpAPI.Response.TX_INFO)

            val success =
                resultCode == com.sumup.merchant.reader.api.SumUpAPI.Response.ResultCode.SUCCESSFUL
            if (success && transactionInfo != null) {
                Toast.makeText(this, "Payment successful! Processing order...", Toast.LENGTH_SHORT)
                    .show()

                // Handle successful payment - save order to server
                currentSumUpOrder?.let { order ->
                    // Update order with transaction info
                    val updatedOrder = order.copy(
                        transactionId = transactionInfo.transactionCode ?: "",
                        paymentType = 6 // Card payment type
                    )

                    handleCashPaymentComplete(updatedOrder)

//                    // Show processing loader and save order
//                    showProcessingDialog()
//                    lifecycleScope.launch {
//                        try {
//                            paymentViewModel.placeOnlineOrder(order = updatedOrder).collectLatest { orderResponse ->
//                                when (orderResponse) {
//                                    is com.airbnb.mvrx.Success -> {
//                                        kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
//                                            hideProcessingDialog()
//                                            // Create Order2 object from successful response
//                                            val order2 = orderResponse().order
//                                            order2?.let { handleSumUpPaymentComplete(it) }
//                                            // Clear the stored order
//                                            currentSumUpOrder = null
//                                        }
//                                    }
//                                    is com.airbnb.mvrx.Fail -> {
//                                        kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
//                                            hideProcessingDialog()
//                                            Toast.makeText(this@CategoriesActivity, "Failed to place order: ${orderResponse.error.message}", Toast.LENGTH_LONG).show()
//                                        }
//                                    }
//                                    is com.airbnb.mvrx.Loading -> {
//                                        // Processing dialog is already shown
//                                    }
//                                    else -> {
//                                        kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
//                                            hideProcessingDialog()
//                                        }
//                                    }
//                                }
//                            }
//                        } catch (e: Exception) {
//                            hideProcessingDialog()
//                            Toast.makeText(this@CategoriesActivity, "Error placing order: ${e.message}", Toast.LENGTH_LONG).show()
//                        }
//                    }
                }
            } else {
                Toast.makeText(this, "SumUp payment failed: $message", Toast.LENGTH_LONG).show()
            }
        } else {
            Toast.makeText(this, "SumUp payment cancelled", Toast.LENGTH_SHORT).show()
        }
    }

    private fun handleSumUpPaymentComplete(order: Order2) {
        // Handle successful SumUp payment completion
        // This is similar to cash payment completion
//        showSuccessDialogCallback?.invoke("SumUp Payment Completed Successfully!")

        // Clear cart and close modal
        productsScreenViewModel.clearCart()
        productsScreenViewModel.updateCartVisibility(false)

        // Close the cart modal using the callback
        closeCartModal()
    }

    fun showOrderSucessSnackBar() {
        val snackbar = Snackbar.make(
            <EMAIL>(android.R.id.content),
            "Order placed successfully!",
            Snackbar.LENGTH_SHORT
        )
        snackbar.show()
    }
}

// Helper function to extract customer ID from QR code format
fun extractCustomerId(input: String): Int? {
    return try {
        // Expected format: dasa-businessId-customerId
        val parts = input.split("-")
        if (parts.size == 3 && parts[0] == "dasa") {
            parts[2].toIntOrNull()
        } else {
            // If not in expected format, try to parse as direct number
            input.toIntOrNull()
        }
    } catch (e: Exception) {
        null
    }
}

fun extractBusinessId(input: String): Int? {
    return try {
        // Expected format: dasa-businessId-customerId
        val parts = input.split("-")
        if (parts.size == 3 && parts[0] == "dasa") {
            parts[1].toIntOrNull()
        } else {
            // If not in expected format, try to parse as direct number
            input.toIntOrNull()
        }
    } catch (e: Exception) {
        null
    }
}