{"project_info": {"project_number": "552125958429", "project_id": "dasadirect", "storage_bucket": "dasadirect.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:552125958429:android:63fae074a22a1e10d6b9f4", "android_client_info": {"package_name": "com.dasadirect.app"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBn8bybiTibHkeRacv0uSoJUEX19kblmBI"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:552125958429:android:34c13364fe6e16d7d6b9f4", "android_client_info": {"package_name": "com.dasadirect.dasaapp"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBn8bybiTibHkeRacv0uSoJUEX19kblmBI"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:552125958429:android:657dd6d29be84b9cd6b9f4", "android_client_info": {"package_name": "com.dasadirect.dasaappsumnitest"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBn8bybiTibHkeRacv0uSoJUEX19kblmBI"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:552125958429:android:62da8feb4d68fe94d6b9f4", "android_client_info": {"package_name": "com.dasadirect.dasapos"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBn8bybiTibHkeRacv0uSoJUEX19kblmBI"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:552125958429:android:e7a8a4d6e9dd76eed6b9f4", "android_client_info": {"package_name": "com.dasadirect.dasapos2"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBn8bybiTibHkeRacv0uSoJUEX19kblmBI"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:552125958429:android:0b80a2948da6039fd6b9f4", "android_client_info": {"package_name": "com.dasadirect.dasapos3"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBn8bybiTibHkeRacv0uSoJUEX19kblmBI"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:552125958429:android:20c08527d9705262d6b9f4", "android_client_info": {"package_name": "com.thedasagroup.pos.handheld"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBn8bybiTibHkeRacv0uSoJUEX19kblmBI"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}], "configuration_version": "1"}